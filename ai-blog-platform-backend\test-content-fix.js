// Test content generation fix
require('dotenv').config();

async function testContentGeneration() {
  try {
    console.log('🧪 Testing Content Generation Fix...\n');
    
    const geminiService = require('./services/geminiService');
    
    console.log('📋 Testing structured blog content generation');
    console.log('=============================================');
    
    const draftData = {
      selectedKeyword: 'industrial solar maintenance protocols',
      selectedH1: 'Mastering Industrial Solar Maintenance Protocols: A Comprehensive Guide',
      selectedMetaTitle: 'Industrial Solar Maintenance Protocols - Expert Guide 2024',
      selectedMetaDescription: 'Learn essential industrial solar maintenance protocols to maximize system performance and ROI. Expert tips from WattMonk professionals.',
      companyName: 'WattMonk',
      companyContext: {
        name: 'WattMonk',
        servicesOffered: 'Solar Design, Engineering, Permitting, Installation Support'
      },
      targetWordCount: 1500
    };
    
    console.log(`🎯 Testing with keyword: "${draftData.selectedKeyword}"`);
    
    const result = await geminiService.generateStructuredBlogContent(draftData);
    
    console.log('✅ Content generation result:', {
      success: result.success || 'unknown',
      wordCount: result.wordCount || 'unknown',
      hasInternalLinks: result.content?.internalLinks?.length > 0,
      hasExternalLinks: result.content?.externalLinks?.length > 0,
      contentLength: result.content?.content?.length || 0
    });
    
    if (result.success && result.content?.content) {
      console.log('\n📝 Content Preview (first 500 chars):');
      console.log('=====================================');
      console.log(result.content.content.substring(0, 500) + '...');
      
      // Check for proper link formatting
      const hasProperLinks = result.content.content.includes('<a href=');
      const hasBrokenLinks = result.content.content.includes('" target="_blank"');
      
      console.log('\n🔗 Link Analysis:');
      console.log('=================');
      console.log('✅ Has proper <a href> links:', hasProperLinks);
      console.log('❌ Has broken link formatting:', hasBrokenLinks);
      
      if (hasProperLinks && !hasBrokenLinks) {
        console.log('🎉 Content formatting looks good!');
      } else {
        console.log('⚠️ Content formatting needs improvement');
      }
    } else {
      console.log('❌ Content generation failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testContentGeneration();
