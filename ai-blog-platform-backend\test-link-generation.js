/**
 * Test script to check actual link generation in content
 */

const mongoose = require('mongoose');
const linkService = require('./services/linkService');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/ai-blog-platform', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function testLinkGeneration() {
  console.log('🧪 Testing Link Generation...\n');

  const keyword = 'industrial solar maintenance protocols';
  const companyName = 'Wattmonk';

  try {
    // Test 1: Check link service generation
    console.log('📋 Test 1: Link Service Generation');
    console.log('=====================================');
    
    const links = await linkService.generateInboundOutboundLinks(keyword, companyName);
    
    console.log(`✅ Generated ${links.inboundLinks.length} inbound links:`);
    links.inboundLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. ${link.text}`);
      console.log(`     URL: ${link.url}`);
      console.log(`     Type: ${link.type}\n`);
    });

    console.log(`✅ Generated ${links.outboundLinks.length} outbound links:`);
    links.outboundLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. ${link.text}`);
      console.log(`     URL: ${link.url}`);
      console.log(`     Type: ${link.type}\n`);
    });

    // Test 2: Check SEO optimization service formatting
    console.log('\n📋 Test 2: SEO Service Link Formatting');
    console.log('=====================================');

    const SEOOptimizationService = require('./services/seoOptimizationService');
    const seoService = new SEOOptimizationService();
    
    console.log('🔗 formatLinksForPrompt output:');
    const formattedLinks = seoService.formatLinksForPrompt(links.outboundLinks.slice(0, 3));
    console.log(formattedLinks);
    
    console.log('\n🔗 formatRealLinksOnly output:');
    const realLinksOnly = seoService.formatRealLinksOnly(links.outboundLinks.slice(0, 3));
    console.log(realLinksOnly);

    // Test 3: Generate actual content block
    console.log('\n📋 Test 3: Content Block Generation');
    console.log('=====================================');
    
    const companyContext = {
      name: 'Wattmonk',
      servicesOffered: 'Solar Design, Engineering, Permitting, Installation Support'
    };

    const contentBlock = await seoService.regenerateContentBlock(
      'introduction',
      keyword,
      companyName,
      companyContext,
      200
    );

    console.log('✅ Generated content block:');
    console.log('Content:', contentBlock.content);
    console.log('Word count:', contentBlock.wordCount);
    console.log('SEO optimized:', contentBlock.seoOptimized);

    // Test 4: Check for fake links in content
    console.log('\n📋 Test 4: Link Analysis in Content');
    console.log('=====================================');
    
    const content = contentBlock.content;
    
    // Check for fake links
    const fakeLinks = [
      /localhost/gi,
      /\[object\s+Object\]/gi,
      /href="[^"]*debug[^"]*"/gi,
      /href="[^"]*undefined[^"]*"/gi,
      /href=""/gi
    ];

    let hasFakeLinks = false;
    fakeLinks.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        console.log(`❌ Found fake link pattern ${index + 1}:`, matches);
        hasFakeLinks = true;
      }
    });

    if (!hasFakeLinks) {
      console.log('✅ No fake links detected in content');
    }

    // Extract all links from content
    const linkRegex = /<a[^>]+href="([^"]*)"[^>]*>(.*?)<\/a>/gi;
    const extractedLinks = [];
    let match;
    
    while ((match = linkRegex.exec(content)) !== null) {
      extractedLinks.push({
        url: match[1],
        text: match[2].replace(/<[^>]*>/g, ''), // Remove any inner HTML
        fullMatch: match[0]
      });
    }

    console.log(`\n🔗 Found ${extractedLinks.length} links in content:`);
    extractedLinks.forEach((link, index) => {
      console.log(`  ${index + 1}. Text: "${link.text}"`);
      console.log(`     URL: ${link.url}`);
      console.log(`     Valid URL: ${link.url.startsWith('http')}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the test
testLinkGeneration();
