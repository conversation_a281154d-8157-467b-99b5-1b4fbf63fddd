/**
 * Test image generation to verify it's working
 */

// Load environment variables FIRST
require('dotenv').config();

const geminiService = require('./services/geminiService');
const imageService = require('./services/imageService');

async function testImageGeneration() {
  console.log('🧪 Testing Image Generation...\n');

  try {
    console.log('📋 Test 1: Direct Image Service');
    console.log('================================');
    
    const prompt = 'Solar panels on industrial building with maintenance workers';
    console.log(`🎨 Testing image generation with prompt: "${prompt}"`);
    
    // Test direct image service call
    try {
      const result = await imageService.generateImageWithAI(prompt, 'realistic', 'featured');
      console.log('✅ Direct image service result:', {
        success: result.success || 'unknown',
        url: result.url || 'no URL',
        message: result.message || 'no message'
      });
    } catch (error) {
      console.log('❌ Direct image service failed:', error.message);
    }

    console.log('\n📋 Test 2: Gemini Service Image Generation');
    console.log('==========================================');
    
    // Test gemini service image generation (should redirect to image service)
    try {
      const result = await geminiService.generateImages(prompt, {
        style: 'realistic',
        imageType: 'featured'
      });
      console.log('✅ Gemini service result:', {
        success: result.success,
        images: result.images ? result.images.length : 0,
        message: result.message,
        error: result.error || 'none'
      });
    } catch (error) {
      console.log('❌ Gemini service failed:', error.message);
    }

    console.log('\n📋 Test 3: Check Available Methods');
    console.log('==================================');
    
    console.log('Gemini service methods:');
    const geminiMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(geminiService))
      .filter(method => typeof geminiService[method] === 'function' && method !== 'constructor');
    geminiMethods.forEach(method => {
      console.log(`  - ${method}`);
    });

    console.log('\nImage service methods:');
    const imageMethods = Object.getOwnPropertyNames(Object.getPrototypeOf(imageService))
      .filter(method => typeof imageService[method] === 'function' && method !== 'constructor');
    imageMethods.forEach(method => {
      console.log(`  - ${method}`);
    });

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testImageGeneration();
