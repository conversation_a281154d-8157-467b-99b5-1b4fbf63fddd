// Simple server test
const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5002;

// Basic middleware
app.use(express.json());
app.use(cors());

// Import routes
const imageRoutes = require('./routes/imageRoutes');

// Use routes
app.use('/api/images', imageRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📋 Health Check: http://localhost:${PORT}/health`);
});
