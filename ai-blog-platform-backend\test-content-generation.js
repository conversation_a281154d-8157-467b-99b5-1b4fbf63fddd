/**
 * Test actual content generation to see what links are embedded
 */

// Load environment variables FIRST
require('dotenv').config();

const linkService = require('./services/linkService');
const seoService = require('./services/seoOptimizationService');

async function testContentGeneration() {
  console.log('🧪 Testing Content Generation with Links...\n');

  const keyword = 'industrial solar maintenance protocols';
  const companyName = 'Wattmonk';

  try {
    // Step 1: Generate links
    console.log('📋 Step 1: Generating Links');
    console.log('============================');
    
    const links = await linkService.generateInboundOutboundLinks(keyword, companyName);
    
    console.log(`✅ Generated ${links.inboundLinks.length} internal links and ${links.outboundLinks.length} external links`);

    // Step 2: Test SEO service formatting
    console.log('\n📋 Step 2: Testing Link Formatting');
    console.log('====================================');
    
    // seoService is already imported as singleton
    
    // Test the formatRealLinksOnly method
    console.log('🔗 Testing formatRealLinksOnly:');
    const realLinksFormatted = seoService.formatRealLinksOnly(links.outboundLinks.slice(0, 3));
    console.log(realLinksFormatted);

    // Step 3: Generate a content block
    console.log('\n📋 Step 3: Generating Content Block');
    console.log('====================================');
    
    const companyContext = {
      name: 'Wattmonk',
      servicesOffered: 'Solar Design, Engineering, Permitting, Installation Support'
    };

    console.log('🤖 Generating introduction block...');
    const contentBlock = await seoService.regenerateContentBlock(
      'introduction',
      keyword,
      companyName,
      companyContext,
      200
    );

    console.log(`✅ Generated content (${contentBlock.wordCount} words):`);
    console.log('Content:', contentBlock.content);

    // Step 4: Analyze the generated content for links
    console.log('\n📋 Step 4: Analyzing Generated Content');
    console.log('======================================');
    
    const content = contentBlock.content;
    
    // Extract all links
    const linkRegex = /<a[^>]+href="([^"]*)"[^>]*>(.*?)<\/a>/gi;
    const extractedLinks = [];
    let match;
    
    while ((match = linkRegex.exec(content)) !== null) {
      extractedLinks.push({
        url: match[1],
        text: match[2].replace(/<[^>]*>/g, ''),
        fullMatch: match[0]
      });
    }

    console.log(`🔗 Found ${extractedLinks.length} links in generated content:`);
    
    if (extractedLinks.length === 0) {
      console.log('   No links found in content');
    } else {
      extractedLinks.forEach((link, index) => {
        console.log(`\n   ${index + 1}. Text: "${link.text}"`);
        console.log(`      URL: ${link.url}`);
        console.log(`      Valid URL: ${link.url.startsWith('http') ? '✅' : '❌'}`);
        
        // Check for fake link patterns
        const fakePatterns = [
          { pattern: /localhost/i, name: 'localhost' },
          { pattern: /\[object/i, name: '[object' },
          { pattern: /debug/i, name: 'debug' },
          { pattern: /undefined/i, name: 'undefined' },
          { pattern: /^$/, name: 'empty' }
        ];

        let isFake = false;
        fakePatterns.forEach(({ pattern, name }) => {
          if (pattern.test(link.url)) {
            console.log(`      🚨 FAKE LINK: Contains ${name}`);
            isFake = true;
          }
        });

        if (!isFake && link.url.startsWith('http')) {
          console.log(`      ✅ REAL LINK: Looks valid`);
        }
      });
    }

    // Step 5: Check if content was cleaned
    console.log('\n📋 Step 5: Checking Content Cleaning');
    console.log('====================================');
    
    const fakePatterns = [
      /localhost/gi,
      /\[object\s+Object\]/gi,
      /href="[^"]*debug[^"]*"/gi,
      /href="[^"]*undefined[^"]*"/gi,
      /href=""/gi
    ];

    let foundFakePatterns = false;
    fakePatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches) {
        console.log(`❌ Found fake pattern ${index + 1}:`, matches);
        foundFakePatterns = true;
      }
    });

    if (!foundFakePatterns) {
      console.log('✅ No fake link patterns detected in content');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testContentGeneration();
