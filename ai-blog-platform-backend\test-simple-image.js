/**
 * Simple test to check image generation methods
 */

// Load environment variables FIRST
require('dotenv').config();

const geminiService = require('./services/geminiService');

async function testSimple() {
  console.log('🧪 Testing Image Generation Methods...\n');

  try {
    console.log('📋 Checking Gemini Service Methods');
    console.log('==================================');
    
    console.log('Available methods in geminiService:');
    const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(geminiService))
      .filter(method => typeof geminiService[method] === 'function' && method !== 'constructor');
    
    methods.forEach(method => {
      console.log(`  ✅ ${method}`);
    });

    // Check if generateImages method exists
    if (typeof geminiService.generateImages === 'function') {
      console.log('\n✅ generateImages method is available');
      
      // Test with a simple call
      console.log('🧪 Testing generateImages method...');
      const result = await geminiService.generateImages('test prompt', { style: 'realistic' });
      console.log('Result:', {
        success: result.success,
        message: result.message,
        error: result.error || 'none'
      });
    } else {
      console.log('\n❌ generateImages method is NOT available');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSimple();
