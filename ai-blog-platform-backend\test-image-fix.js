// Test image generation fix
require('dotenv').config();

async function testImageGeneration() {
  try {
    console.log('🧪 Testing Image Generation Fix...\n');
    
    const imageService = require('./services/imageService');
    
    console.log('📋 Testing imageService.generateImageWithAI');
    console.log('==========================================');
    
    const prompt = 'Solar panels on modern house roof';
    console.log(`🎨 Testing with prompt: "${prompt}"`);
    
    const result = await imageService.generateImageWithAI(prompt, 'realistic', 'featured');
    
    console.log('✅ Image generation result:', {
      success: result.success || 'unknown',
      imageUrl: result.imageUrl ? 'URL provided' : 'No URL',
      source: result.source || 'unknown',
      error: result.error || 'none'
    });
    
    if (result.success !== false && result.imageUrl) {
      console.log('🎉 Image generation working!');
    } else {
      console.log('⚠️ Image generation returned fallback');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testImageGeneration();
