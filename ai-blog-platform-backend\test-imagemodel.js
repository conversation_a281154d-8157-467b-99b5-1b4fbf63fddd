/**
 * Test imageModel availability
 */

// Load environment variables FIRST
require('dotenv').config();

console.log('🧪 Testing ImageModel...\n');

try {
  const geminiService = require('./services/geminiService');
  
  console.log('📋 Checking imageModel availability');
  console.log('===================================');
  
  if (geminiService.imageModel) {
    console.log('✅ imageModel is available');
    console.log('imageModel methods:', Object.keys(geminiService.imageModel));
    
    if (typeof geminiService.imageModel.generateImages === 'function') {
      console.log('✅ generateImages method is available');
    } else {
      console.log('❌ generateImages method is NOT available');
    }
  } else {
    console.log('❌ imageModel is NOT available');
  }

  console.log('\n📋 Testing imageModel.generateImages');
  console.log('====================================');
  
  // Test the method without actually generating an image
  if (geminiService.imageModel && typeof geminiService.imageModel.generateImages === 'function') {
    console.log('✅ Method exists and is callable');
    console.log('🎯 Frontend should now be able to call imageModel.generateImages()');
  } else {
    console.log('❌ Method is not available');
  }

} catch (error) {
  console.error('❌ Error:', error.message);
}
