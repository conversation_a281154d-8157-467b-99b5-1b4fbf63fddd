// services/imagen3Service.js
const { VertexAI } = require('@google-cloud/vertexai');
const path = require('path');
require('dotenv').config();

class Imagen3Service {
  constructor() {
    // Initialize Vertex AI client for Imagen 3
    this.project = process.env.GOOGLE_CLOUD_PROJECT ||
                   process.env.VERTEX_AI_PROJECT ||
                   process.env.VERTEX_AI_PROJECT_ID;
    this.location = process.env.GOOGLE_CLOUD_LOCATION ||
                    process.env.VERTEX_AI_LOCATION ||
                    process.env.VERTEX_AI_REGION ||
                    'us-central1';

    if (!this.project) {
      console.warn('⚠️ GOOGLE_CLOUD_PROJECT not set. Imagen 3 service disabled.');
      this.vertexAI = null;
      this.imageModel = null;
      return;
    }

    try {
      // Set up authentication
      const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS ||
                                path.join(__dirname, '..', 'service_account_key.json');

      if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        process.env.GOOGLE_APPLICATION_CREDENTIALS = serviceAccountPath;
      }

      // Initialize Vertex AI
      this.vertexAI = new VertexAI({
        project: this.project,
        location: this.location,
      });

      // Use Imagen 3 model
      this.model = 'imagen-3.0-generate-002';

      console.log(`✅ Imagen 3 Service initialized successfully!`);
      console.log(`   Project: ${this.project}`);
      console.log(`   Location: ${this.location}`);
      console.log(`   Model: ${this.model}`);
    } catch (error) {
      console.error('❌ Failed to initialize Imagen 3 Service:', error.message);
      this.vertexAI = null;
      this.imageModel = null;
    }
  }

  /**
   * Generate high-quality images using Imagen 3
   * @param {string} prompt - Image generation prompt
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Generated image result
   */
  async generateImage(prompt, options = {}) {
    if (!this.vertexAI) {
      throw new Error('Imagen 3 service not initialized');
    }

    const {
      numberOfImages = 1,
      aspectRatio = '1:1',
      safetyFilterLevel = 'block_some',
      personGeneration = 'dont_allow',
      outputMimeType = 'image/jpeg'
    } = options;

    try {
      console.log(`🎨 Generating image with Imagen 3: "${prompt}"`);

      // Enhanced prompt for solar industry with professional quality
      const enhancedPrompt = `${prompt}, professional solar industry photography, high quality, modern technology, clean energy, commercial grade, photorealistic, detailed, sharp focus, professional lighting, 4K resolution`;

      // Imagen 3 generation request
      const request = {
        prompt: enhancedPrompt,
        numberOfImages: numberOfImages,
        aspectRatio: aspectRatio,
        safetyFilterLevel: safetyFilterLevel,
        personGeneration: personGeneration,
        outputMimeType: outputMimeType,
        includeRaiReason: true,
        includeSafetyAttributes: true
      };

      // Get the Imagen 3 model
      const imageModel = this.vertexAI.getGenerativeModel({
        model: this.model
      });

      console.log(`📝 Imagen 3 request: ${JSON.stringify(request, null, 2)}`);

      // Check if generateImages method exists
      if (!imageModel.generateImages || typeof imageModel.generateImages !== 'function') {
        throw new Error('Imagen 3 generateImages method not available. Please check Vertex AI SDK version.');
      }

      // Generate the image
      const result = await imageModel.generateImages(request);

      if (!result || !result.generatedImages || result.generatedImages.length === 0) {
        throw new Error('No images generated by Imagen 3');
      }

      const generatedImage = result.generatedImages[0];
      
      console.log(`✅ Imagen 3 generated image successfully`);
      console.log(`   Safety attributes: ${JSON.stringify(generatedImage.safetyAttributes)}`);
      console.log(`   RAI reason: ${generatedImage.raiReason || 'None'}`);

      return {
        success: true,
        imageData: generatedImage.image.imageBytes,
        mimeType: generatedImage.image.mimeType || outputMimeType,
        safetyAttributes: generatedImage.safetyAttributes,
        raiReason: generatedImage.raiReason,
        prompt: enhancedPrompt,
        model: this.model,
        source: 'imagen-3'
      };

    } catch (error) {
      console.error('❌ Imagen 3 generation failed:', error.message);
      
      // Log specific error types
      if (error.code === 'PERMISSION_DENIED') {
        console.error('🔑 Authentication issue - check service account permissions for Imagen 3');
      } else if (error.code === 'QUOTA_EXCEEDED') {
        console.error('📊 Quota exceeded - check Imagen 3 usage limits');
      } else if (error.code === 'INVALID_ARGUMENT') {
        console.error('📝 Invalid request - check Imagen 3 parameters');
      }

      throw error;
    }
  }

  /**
   * Generate dynamic image prompts optimized for Imagen 3
   * @param {string} keyword - Focus keyword
   * @param {string} imageType - Type of image (feature, content, etc.)
   * @param {string} companyName - Company name for branding
   * @returns {string} Optimized prompt
   */
  generateOptimizedPrompt(keyword, imageType = 'feature', companyName = 'WattMonk') {
    const basePrompts = {
      feature: [
        `Professional ${keyword} installation with modern solar technology`,
        `High-tech ${keyword} system with advanced solar equipment`,
        `Commercial ${keyword} setup with professional grade components`,
        `Industrial ${keyword} facility with cutting-edge solar technology`
      ],
      content: [
        `Technical diagram showing ${keyword} components and connections`,
        `Step-by-step ${keyword} installation process with professional workers`,
        `Detailed view of ${keyword} equipment with technical specifications`,
        `Professional team working on ${keyword} project with safety equipment`
      ],
      technical: [
        `Close-up view of ${keyword} technical components and wiring`,
        `Engineering blueprint style ${keyword} system diagram`,
        `Professional ${keyword} monitoring and control systems`,
        `Technical analysis of ${keyword} performance metrics and data`
      ]
    };

    const prompts = basePrompts[imageType] || basePrompts.feature;
    const selectedPrompt = prompts[Math.floor(Math.random() * prompts.length)];

    // Add professional quality modifiers
    const qualityModifiers = [
      'professional photography',
      'high resolution',
      'commercial quality',
      'industry standard',
      'photorealistic',
      'detailed composition',
      'professional lighting',
      'sharp focus',
      'modern aesthetic'
    ];

    const modifier = qualityModifiers[Math.floor(Math.random() * qualityModifiers.length)];

    return `${selectedPrompt}, ${modifier}, solar industry, clean energy technology, ${companyName} branding style`;
  }

  /**
   * Check if Imagen 3 service is available
   * @returns {boolean} Service availability
   */
  isAvailable() {
    return this.vertexAI !== null;
  }

  /**
   * Get service status and configuration
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      available: this.isAvailable(),
      project: this.project,
      location: this.location,
      model: this.model,
      service: 'Imagen 3'
    };
  }
}

// Export as singleton
module.exports = new Imagen3Service();
