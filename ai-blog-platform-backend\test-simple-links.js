/**
 * Simple test to check what links are actually in generated content
 */

const mongoose = require('mongoose');
const Draft = require('./models/Draft');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/ai-blog-platform', {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

async function checkExistingContent() {
  console.log('🔍 Checking existing draft content for links...\n');

  try {
    // Find the most recent draft
    const draft = await Draft.findOne().sort({ createdAt: -1 });
    
    if (!draft) {
      console.log('❌ No drafts found');
      return;
    }

    console.log(`✅ Found draft: ${draft._id}`);
    console.log(`📝 Keyword: ${draft.selectedKeyword}`);
    console.log(`🏢 Company: ${draft.blogId?.companyId?.name || 'Unknown'}`);
    
    if (!draft.generatedContent?.contentBlocks) {
      console.log('❌ No content blocks found');
      return;
    }

    console.log(`📋 Found ${draft.generatedContent.contentBlocks.length} content blocks\n`);

    // Check each content block for links
    draft.generatedContent.contentBlocks.forEach((block, index) => {
      console.log(`📝 Block ${index + 1} (${block.type}):`);
      console.log(`   Word count: ${block.wordCount || 'unknown'}`);
      
      if (block.content) {
        // Extract all links from content
        const linkRegex = /<a[^>]+href="([^"]*)"[^>]*>(.*?)<\/a>/gi;
        const extractedLinks = [];
        let match;
        
        while ((match = linkRegex.exec(block.content)) !== null) {
          extractedLinks.push({
            url: match[1],
            text: match[2].replace(/<[^>]*>/g, ''), // Remove any inner HTML
          });
        }

        if (extractedLinks.length > 0) {
          console.log(`   🔗 Found ${extractedLinks.length} links:`);
          extractedLinks.forEach((link, linkIndex) => {
            console.log(`      ${linkIndex + 1}. "${link.text}"`);
            console.log(`         URL: ${link.url}`);
            console.log(`         Valid: ${link.url.startsWith('http') ? '✅' : '❌'}`);
            
            // Check for fake link patterns
            if (link.url.includes('localhost') || 
                link.url.includes('[object') || 
                link.url.includes('debug') || 
                link.url === '') {
              console.log(`         🚨 FAKE LINK DETECTED!`);
            }
          });
        } else {
          console.log(`   📝 No links found in content`);
        }

        // Show a snippet of the content
        const snippet = block.content.substring(0, 200) + (block.content.length > 200 ? '...' : '');
        console.log(`   📄 Content snippet: ${snippet}`);
      }
      
      console.log(''); // Empty line for readability
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the test
checkExistingContent();
